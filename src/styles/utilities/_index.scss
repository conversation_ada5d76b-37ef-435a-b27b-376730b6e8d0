// =================================================================
// Galaxy Design System v3.0 - Utility Classes
// =================================================================
@import '../tokens/colors';
@import '../tokens/typography';

// Text utilities
@each $name, $color in $colors {
  .text-#{$name} {
    color: #{$color} !important;
  }
}

// Additional semantic text colors
.text-success {
  color: var(--color-green-main) !important;
}
.text-warning {
  color: var(--color-semantic-warning) !important;
}
.text-error {
  color: var(--color-semantic-error) !important;
}
.text-info {
  color: var(--color-semantic-info) !important;
}

// Typography utilities
@each $size, $props in $typography-scale {
  .text-#{$size} {
    font-size: map-get($props, font-size);
    line-height: map-get($props, line-height);
  }
}

// Font weight utilities
.font-normal {
  font-weight: var(--font-weight-normal) !important;
}
.font-medium {
  font-weight: var(--font-weight-medium) !important;
}
.font-semibold {
  font-weight: var(--font-weight-semibold) !important;
}
.font-bold {
  font-weight: var(--font-weight-bold) !important;
}

// Font family utilities
.font-chinese {
  font-family: var(--font-family-chinese) !important;
}
.font-english {
  font-family: var(--font-family-english) !important;
}
.font-numeric {
  font-family: var(--font-family-numeric) !important;
}
.font-mono {
  font-family: var(--font-family-mono) !important;
}

// Spacing utilities
@each $key, $value in $spacing-scale {
  // Margin
  .m-#{$key} {
    margin: #{$value} !important;
  }
  .mt-#{$key} {
    margin-top: #{$value} !important;
  }
  .mr-#{$key} {
    margin-right: #{$value} !important;
  }
  .mb-#{$key} {
    margin-bottom: #{$value} !important;
  }
  .ml-#{$key} {
    margin-left: #{$value} !important;
  }
  .mx-#{$key} {
    margin-left: #{$value} !important;
    margin-right: #{$value} !important;
  }
  .my-#{$key} {
    margin-top: #{$value} !important;
    margin-bottom: #{$value} !important;
  }

  // Padding
  .p-#{$key} {
    padding: #{$value} !important;
  }
  .pt-#{$key} {
    padding-top: #{$value} !important;
  }
  .pr-#{$key} {
    padding-right: #{$value} !important;
  }
  .pb-#{$key} {
    padding-bottom: #{$value} !important;
  }
  .pl-#{$key} {
    padding-left: #{$value} !important;
  }
  .px-#{$key} {
    padding-left: #{$value} !important;
    padding-right: #{$value} !important;
  }
  .py-#{$key} {
    padding-top: #{$value} !important;
    padding-bottom: #{$value} !important;
  }

  // Gap
  .gap-#{$key} {
    gap: #{$value} !important;
  }
}

// Background utilities
@each $name, $color in $colors {
  .bg-#{$name} {
    background-color: #{$color} !important;
  }
}

// Gradient utilities
.bg-gradient-blue-deep {
  background-image: var(--gradient-blue-deep) !important;
}
.bg-gradient-red-deep {
  background-image: var(--gradient-red-deep) !important;
}
.bg-gradient-blue-light {
  background-image: var(--gradient-blue-light) !important;
}
.bg-gradient-yellow-light {
  background-image: var(--gradient-yellow-light) !important;
}

// Icon gradient utilities
.bg-gradient-galaxy-red {
  background: var(--gradient-galaxy-red) !important;
}
.bg-gradient-galaxy-blue {
  background: var(--gradient-galaxy-blue) !important;
}
.bg-gradient-wealth-gold {
  background: var(--gradient-wealth-gold) !important;
}
.bg-gradient-tech-blue {
  background: var(--gradient-tech-blue) !important;
}
.bg-gradient-vitality-orange {
  background: var(--gradient-vitality-orange) !important;
}

// Border utilities
.border {
  border: 1px solid var(--color-by3) !important;
}
.border-0 {
  border: 0 !important;
}
.border-t {
  border-top: 1px solid var(--color-by3) !important;
}
.border-r {
  border-right: 1px solid var(--color-by3) !important;
}
.border-b {
  border-bottom: 1px solid var(--color-by3) !important;
}
.border-l {
  border-left: 1px solid var(--color-by3) !important;
}

// Border radius utilities
.rounded-none {
  border-radius: var(--radius-none) !important;
}
.rounded-sm {
  border-radius: var(--radius-sm) !important;
}
.rounded {
  border-radius: var(--radius-base) !important;
}
.rounded-md {
  border-radius: var(--radius-md) !important;
}
.rounded-lg {
  border-radius: var(--radius-lg) !important;
}
.rounded-xl {
  border-radius: var(--radius-xl) !important;
}
.rounded-2xl {
  border-radius: var(--radius-2xl) !important;
}
.rounded-full {
  border-radius: var(--radius-full) !important;
}

// Shadow utilities
.shadow-none {
  box-shadow: none !important;
}
.shadow-sm {
  box-shadow: var(--shadow-sm) !important;
}
.shadow {
  box-shadow: var(--shadow-base) !important;
}
.shadow-md {
  box-shadow: var(--shadow-md) !important;
}
.shadow-lg {
  box-shadow: var(--shadow-lg) !important;
}
.shadow-xl {
  box-shadow: var(--shadow-xl) !important;
}

// Display utilities
.block {
  display: block !important;
}
.inline-block {
  display: inline-block !important;
}
.inline {
  display: inline !important;
}
.flex {
  display: flex !important;
}
.inline-flex {
  display: inline-flex !important;
}
.grid {
  display: grid !important;
}
.hidden {
  display: none !important;
}

// Flexbox utilities
.flex-row {
  flex-direction: row !important;
}
.flex-col {
  flex-direction: column !important;
}
.flex-wrap {
  flex-wrap: wrap !important;
}
.flex-nowrap {
  flex-wrap: nowrap !important;
}
.items-start {
  align-items: flex-start !important;
}
.items-center {
  align-items: center !important;
}
.items-end {
  align-items: flex-end !important;
}
.justify-start {
  justify-content: flex-start !important;
}
.justify-center {
  justify-content: center !important;
}
.justify-end {
  justify-content: flex-end !important;
}
.justify-between {
  justify-content: space-between !important;
}

// Position utilities
.relative {
  position: relative !important;
}
.absolute {
  position: absolute !important;
}
.fixed {
  position: fixed !important;
}
.sticky {
  position: sticky !important;
}

// Overflow utilities
.overflow-hidden {
  overflow: hidden !important;
}
.overflow-auto {
  overflow: auto !important;
}
.overflow-scroll {
  overflow: scroll !important;
}

// Opacity utilities
.opacity-0 {
  opacity: 0 !important;
}
.opacity-25 {
  opacity: 0.25 !important;
}
.opacity-50 {
  opacity: 0.5 !important;
}
.opacity-75 {
  opacity: 0.75 !important;
}
.opacity-100 {
  opacity: 1 !important;
}

// Cursor utilities
.cursor-pointer {
  cursor: pointer !important;
}
.cursor-not-allowed {
  cursor: not-allowed !important;
}
.cursor-default {
  cursor: default !important;
}

// User select utilities
.select-none {
  user-select: none !important;
}
.select-text {
  user-select: text !important;
}
.select-all {
  user-select: all !important;
}
