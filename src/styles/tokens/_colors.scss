// =================================================================
// Galaxy Design System v3.0 - Complete Color System
// =================================================================

:root {
  // ===== MAIN BRAND COLORS =====
  --color-blue-main: #434DBF;
  --color-orange-main: #EBA21E;
  --color-red-main: #D94C4C;
  --color-green-main: #248360;

  // ===== COMPLETE GRAY SCALE PALETTE =====
  --color-by8: #090A1A;  // Primary Text - Darkest
  --color-by7: #656870;  // Secondary Text
  --color-by6: #A3ACBF;  // Tertiary Text, Neutral Gray
  --color-by5: #B8C2D9;  // Light Gray Text
  --color-by4: #D9DCE6;  // Stroke, Border
  --color-by3: #E7EBF2;  // Divider, Light Border
  --color-by2: #F7F8FC;  // Disabled Background
  --color-by1: #FFFFFF;  // White
  --color-bg: #F4F5F6;   // App Background

  // ===== COMPLETE BLUE PALETTE =====
  --color-bu9: #2A2E7A;  // Darkest Blue
  --color-bu8: #363699;  // Very Dark Blue
  --color-bu7: #434DBF;  // Main Blue
  --color-bu6: #5761D9;  // Medium Blue
  --color-bu5: #8594F2;  // Light Blue
  --color-bu4: #A9AFF1;  // Lighter Blue
  --color-bu3: #C4C9F7;  // Very Light Blue
  --color-bu2: #E6EAFF;  // Ultra Light Blue
  --color-bu1: #F7F9FF;  // Lightest Blue
  --color-bu-alt: #33A0FF; // Alternative Blue

  // ===== COMPLETE ORANGE PALETTE =====
  --color-oe9: #B8651A;  // Darkest Orange
  --color-oe8: #D17A22;  // Very Dark Orange
  --color-oe7: #E5842A;  // Dark Orange
  --color-oe6: #EBA21E;  // Main Orange
  --color-oe5: #F2B854;  // Medium Orange
  --color-oe4: #FFE4C9;  // Light Orange
  --color-oe3: #FFF2CC;  // Lighter Orange
  --color-oe2: #FFF8E6;  // Very Light Orange
  --color-oe1: #FCFAF7;  // Lightest Orange

  // ===== COMPLETE RED PALETTE =====
  --color-rd9: #8A1F1F;  // Darkest Red
  --color-rd8: #AA2828;  // Very Dark Red
  --color-rd7: #D94C4C;  // Main Red
  --color-rd6: #E67267;  // Medium Red
  --color-rd5: #F28E85;  // Light Red
  --color-rd4: #F7A8A0;  // Lighter Red
  --color-rd3: #FBC2BC;  // Very Light Red
  --color-rd2: #FFE8E6;  // Ultra Light Red
  --color-rd1: #FFFAF9;  // Lightest Red

  // ===== COMPLETE GREEN PALETTE =====
  --color-gn9: #1A5C42;  // Darkest Green
  --color-gn8: #1F6B4A;  // Very Dark Green
  --color-gn7: #248360;  // Main Green
  --color-gn6: #2E9B73;  // Medium Green
  --color-gn5: #4DB396;  // Light Green
  --color-gn4: #7CCBB3;  // Lighter Green
  --color-gn3: #A8E3CF;  // Very Light Green
  --color-gn2: #D4F1E8;  // Ultra Light Green
  --color-gn1: #F0FBF7;  // Lightest Green

  // ===== COMPLETE GOLD PALETTE =====
  --color-gd9: #6B4226;  // Darkest Gold
  --color-gd8: #905A35;  // Very Dark Gold
  --color-gd7: #C8966F;  // Dark Gold
  --color-gd6: #E6B885;  // Medium Gold
  --color-gd5: #F2CE85;  // Light Gold
  --color-gd4: #F7D9A3;  // Lighter Gold
  --color-gd3: #FBE4C1;  // Very Light Gold
  --color-gd2: #FBF5EC;  // Ultra Light Gold
  --color-gd1: #FEFCF9;  // Lightest Gold

  // ===== PURPLE PALETTE =====
  --color-pu9: #4C1D95;  // Darkest Purple
  --color-pu8: #5B21B6;  // Very Dark Purple
  --color-pu7: #7C3AED;  // Main Purple
  --color-pu6: #8B5CF6;  // Medium Purple
  --color-pu5: #A78BFA;  // Light Purple
  --color-pu4: #C4B5FD;  // Lighter Purple
  --color-pu3: #DDD6FE;  // Very Light Purple
  --color-pu2: #EDE9FE;  // Ultra Light Purple
  --color-pu1: #F5F3FF;  // Lightest Purple

  // ===== TEAL PALETTE =====
  --color-tl9: #134E4A;  // Darkest Teal
  --color-tl8: #155E75;  // Very Dark Teal
  --color-tl7: #0891B2;  // Main Teal
  --color-tl6: #0EA5E9;  // Medium Teal
  --color-tl5: #38BDF8;  // Light Teal
  --color-tl4: #7DD3FC;  // Lighter Teal
  --color-tl3: #BAE6FD;  // Very Light Teal
  --color-tl2: #E0F2FE;  // Ultra Light Teal
  --color-tl1: #F0F9FF;  // Lightest Teal

  // ===== SEMANTIC COLORS =====
  --color-semantic-info: var(--color-blue-main);
  --color-semantic-success: var(--color-green-main);
  --color-semantic-warning: #EFAE54;
  --color-semantic-error: var(--color-red-main);
  --color-semantic-neutral: var(--color-by6);

  // ===== STATUS COLORS =====
  --color-status-in-progress: var(--color-bu6);
  --color-status-incomplete: var(--color-by6);
  --color-status-complete: var(--color-gn6);
  --color-status-cancelled: var(--color-rd6);
  --color-status-pending: var(--color-oe6);
  --color-status-draft: var(--color-pu6);

  // ===== TRANSACTION COLORS =====
  --color-transaction-buy: var(--color-red-main);
  --color-transaction-sell: var(--color-blue-main);
  --color-transaction-cancel: var(--color-by6);
  --color-transaction-hold: var(--color-oe6);

  // ===== DEEP GRADIENTS =====
  --gradient-blue-deep: linear-gradient(135deg, var(--color-bu8) 0%, var(--color-bu6) 100%);
  --gradient-red-deep: linear-gradient(135deg, var(--color-rd8) 0%, var(--color-rd6) 100%);
  --gradient-green-deep: linear-gradient(135deg, var(--color-gn8) 0%, var(--color-gn6) 100%);
  --gradient-orange-deep: linear-gradient(135deg, var(--color-oe8) 0%, var(--color-oe6) 100%);
  --gradient-purple-deep: linear-gradient(135deg, var(--color-pu8) 0%, var(--color-pu6) 100%);
  --gradient-teal-deep: linear-gradient(135deg, var(--color-tl8) 0%, var(--color-tl6) 100%);

  // ===== LIGHT GRADIENTS =====
  --gradient-blue-light: linear-gradient(135deg, var(--color-bu2) 0%, var(--color-bu1) 100%);
  --gradient-red-light: linear-gradient(135deg, var(--color-rd2) 0%, var(--color-rd1) 100%);
  --gradient-green-light: linear-gradient(135deg, var(--color-gn2) 0%, var(--color-gn1) 100%);
  --gradient-orange-light: linear-gradient(135deg, var(--color-oe2) 0%, var(--color-oe1) 100%);
  --gradient-purple-light: linear-gradient(135deg, var(--color-pu2) 0%, var(--color-pu1) 100%);
  --gradient-teal-light: linear-gradient(135deg, var(--color-tl2) 0%, var(--color-tl1) 100%);
  --gradient-gold-light: linear-gradient(135deg, var(--color-gd2) 0%, var(--color-gd1) 100%);
  --gradient-gray-light: linear-gradient(135deg, var(--color-by2) 0%, var(--color-by1) 100%);

  // ===== MEDIUM GRADIENTS =====
  --gradient-blue-medium: linear-gradient(135deg, var(--color-bu4) 0%, var(--color-bu2) 100%);
  --gradient-red-medium: linear-gradient(135deg, var(--color-rd4) 0%, var(--color-rd2) 100%);
  --gradient-green-medium: linear-gradient(135deg, var(--color-gn4) 0%, var(--color-gn2) 100%);
  --gradient-orange-medium: linear-gradient(135deg, var(--color-oe4) 0%, var(--color-oe2) 100%);
  --gradient-purple-medium: linear-gradient(135deg, var(--color-pu4) 0%, var(--color-pu2) 100%);
  --gradient-teal-medium: linear-gradient(135deg, var(--color-tl4) 0%, var(--color-tl2) 100%);

  // ===== BRAND ICON GRADIENTS =====
  --gradient-galaxy-red: linear-gradient(45deg, #D94C4C 0%, #E67267 50%, #F28E85 100%);
  --gradient-galaxy-blue: linear-gradient(45deg, #5761D9 0%, #8594F2 50%, #A9AFF1 100%);
  --gradient-wealth-gold: linear-gradient(45deg, #EBA21E 0%, #F2CE85 50%, #F7D9A3 100%);
  --gradient-tech-blue: linear-gradient(45deg, #4470F5 0%, #85ABF6 50%, #B3C7F7 100%);
  --gradient-vitality-orange: linear-gradient(45deg, #ECA05A 0%, #FCB371 50%, #FDCB88 100%);
  --gradient-nature-green: linear-gradient(45deg, #248360 0%, #4DB396 50%, #7CCBB3 100%);
  --gradient-royal-purple: linear-gradient(45deg, #7C3AED 0%, #A78BFA 50%, #C4B5FD 100%);
  --gradient-ocean-teal: linear-gradient(45deg, #0891B2 0%, #38BDF8 50%, #7DD3FC 100%);

  // ===== RAINBOW GRADIENTS =====
  --gradient-rainbow-warm: linear-gradient(90deg, #D94C4C 0%, #EBA21E 25%, #F2CE85 50%, #248360 75%, #434DBF 100%);
  --gradient-rainbow-cool: linear-gradient(90deg, #434DBF 0%, #7C3AED 25%, #0891B2 50%, #248360 75%, #EBA21E 100%);
  --gradient-sunset: linear-gradient(135deg, #D94C4C 0%, #EBA21E 50%, #F2CE85 100%);
  --gradient-ocean: linear-gradient(135deg, #434DBF 0%, #0891B2 50%, #248360 100%);
  --gradient-forest: linear-gradient(135deg, #248360 0%, #7C3AED 50%, #434DBF 100%);
}

// ===== DARK MODE OVERRIDES =====
@media (prefers-color-scheme: dark) {
  :root {
    // Invert gray scale for dark mode
    --color-by8: #FFFFFF;
    --color-by7: #A3ACBF;
    --color-by6: #656870;
    --color-by5: #4A4D5A;
    --color-by4: #2A2D3A;
    --color-by3: #1F2937;
    --color-by2: #111827;
    --color-by1: #090A1A;
    --color-bg: #0F1419;

    // Adjust other colors for dark mode
    --color-bu1: #1E293B;
    --color-rd1: #1F1B1B;
    --color-gn1: #1B2B26;
    --color-oe1: #2B251F;
    --color-pu1: #1E1B2E;
    --color-tl1: #1B252B;
    --color-gd1: #2B261F;
  }
}

// ===== SCSS COLOR MAPS FOR BACKWARD COMPATIBILITY =====
$brand-colors: (
  "blue-main": var(--color-blue-main),
  "orange-main": var(--color-orange-main),
  "red-main": var(--color-red-main),
  "green-main": var(--color-green-main)
);

$gray-colors: (
  "by8": var(--color-by8),
  "by7": var(--color-by7),
  "by6": var(--color-by6),
  "by5": var(--color-by5),
  "by4": var(--color-by4),
  "by3": var(--color-by3),
  "by2": var(--color-by2),
  "by1": var(--color-by1),
  "bg": var(--color-bg)
);

$blue-colors: (
  "bu9": var(--color-bu9),
  "bu8": var(--color-bu8),
  "bu7": var(--color-bu7),
  "bu6": var(--color-bu6),
  "bu5": var(--color-bu5),
  "bu4": var(--color-bu4),
  "bu3": var(--color-bu3),
  "bu2": var(--color-bu2),
  "bu1": var(--color-bu1)
);

$colors: map-merge(map-merge($brand-colors, $gray-colors), $blue-colors);

// ===== GRADIENT MAPS =====
$deep-gradients: (
  "blue": var(--gradient-blue-deep),
  "red": var(--gradient-red-deep),
  "green": var(--gradient-green-deep),
  "orange": var(--gradient-orange-deep),
  "purple": var(--gradient-purple-deep),
  "teal": var(--gradient-teal-deep)
);

$light-gradients: (
  "blue": var(--gradient-blue-light),
  "red": var(--gradient-red-light),
  "green": var(--gradient-green-light),
  "orange": var(--gradient-orange-light),
  "purple": var(--gradient-purple-light),
  "teal": var(--gradient-teal-light),
  "gold": var(--gradient-gold-light),
  "gray": var(--gradient-gray-light)
);

$brand-gradients: (
  "galaxy-red": var(--gradient-galaxy-red),
  "galaxy-blue": var(--gradient-galaxy-blue),
  "wealth-gold": var(--gradient-wealth-gold),
  "tech-blue": var(--gradient-tech-blue),
  "vitality-orange": var(--gradient-vitality-orange),
  "nature-green": var(--gradient-nature-green),
  "royal-purple": var(--gradient-royal-purple),
  "ocean-teal": var(--gradient-ocean-teal)
);
