// =================================================================
// Galaxy Design System v3.0 - Complete Color System
// =================================================================

:root {
  // ===== MAIN BRAND COLORS =====
  --color-blue-main: #434dbf;
  --color-orange-main: #eba21e;
  --color-red-main: #d94c4c;
  --color-green-main: #248360;

  // ===== COMPLETE GRAY SCALE PALETTE =====
  --color-by8: #090a1a; // Primary Text - Darkest
  --color-by7: #656870; // Secondary Text
  --color-by6: #a3acbf; // Tertiary Text, Neutral Gray
  --color-by5: #b8c2d9; // Light Gray Text
  --color-by4: #d9dce6; // Stroke, Border
  --color-by3: #e7ebf2; // Divider, Light Border
  --color-by2: #f7f8fc; // Disabled Background
  --color-by1: #ffffff; // White
  --color-bg: #f4f5f6; // App Background

  // ===== COMPLETE BLUE PALETTE =====
  --color-bu9: #2a2e7a; // Darkest Blue
  --color-bu8: #363699; // Very Dark Blue
  --color-bu7: #434dbf; // Main Blue
  --color-bu6: #5761d9; // Medium Blue
  --color-bu5: #8594f2; // Light Blue
  --color-bu4: #a9aff1; // Lighter Blue
  --color-bu3: #c4c9f7; // Very Light Blue
  --color-bu2: #e6eaff; // Ultra Light Blue
  --color-bu1: #f7f9ff; // Lightest Blue
  --color-bu-alt: #33a0ff; // Alternative Blue

  // ===== COMPLETE ORANGE PALETTE =====
  --color-oe9: #b8651a; // Darkest Orange
  --color-oe8: #d17a22; // Very Dark Orange
  --color-oe7: #e5842a; // Dark Orange
  --color-oe6: #eba21e; // Main Orange
  --color-oe5: #f2b854; // Medium Orange
  --color-oe4: #ffe4c9; // Light Orange
  --color-oe3: #fff2cc; // Lighter Orange
  --color-oe2: #fff8e6; // Very Light Orange
  --color-oe1: #fcfaf7; // Lightest Orange

  // ===== COMPLETE RED PALETTE =====
  --color-rd9: #8a1f1f; // Darkest Red
  --color-rd8: #aa2828; // Very Dark Red
  --color-rd7: #d94c4c; // Main Red
  --color-rd6: #e67267; // Medium Red
  --color-rd5: #f28e85; // Light Red
  --color-rd4: #f7a8a0; // Lighter Red
  --color-rd3: #fbc2bc; // Very Light Red
  --color-rd2: #ffe8e6; // Ultra Light Red
  --color-rd1: #fffaf9; // Lightest Red

  // ===== COMPLETE GREEN PALETTE =====
  --color-gn9: #1a5c42; // Darkest Green
  --color-gn8: #1f6b4a; // Very Dark Green
  --color-gn7: #248360; // Main Green
  --color-gn6: #2e9b73; // Medium Green
  --color-gn5: #4db396; // Light Green
  --color-gn4: #7ccbb3; // Lighter Green
  --color-gn3: #a8e3cf; // Very Light Green
  --color-gn2: #d4f1e8; // Ultra Light Green
  --color-gn1: #f0fbf7; // Lightest Green

  // ===== COMPLETE GOLD PALETTE =====
  --color-gd9: #6b4226; // Darkest Gold
  --color-gd8: #905a35; // Very Dark Gold
  --color-gd7: #c8966f; // Dark Gold
  --color-gd6: #e6b885; // Medium Gold
  --color-gd5: #f2ce85; // Light Gold
  --color-gd4: #f7d9a3; // Lighter Gold
  --color-gd3: #fbe4c1; // Very Light Gold
  --color-gd2: #fbf5ec; // Ultra Light Gold
  --color-gd1: #fefcf9; // Lightest Gold

  // ===== PURPLE PALETTE =====
  --color-pu9: #4c1d95; // Darkest Purple
  --color-pu8: #5b21b6; // Very Dark Purple
  --color-pu7: #7c3aed; // Main Purple
  --color-pu6: #8b5cf6; // Medium Purple
  --color-pu5: #a78bfa; // Light Purple
  --color-pu4: #c4b5fd; // Lighter Purple
  --color-pu3: #ddd6fe; // Very Light Purple
  --color-pu2: #ede9fe; // Ultra Light Purple
  --color-pu1: #f5f3ff; // Lightest Purple

  // ===== TEAL PALETTE =====
  --color-tl9: #134e4a; // Darkest Teal
  --color-tl8: #155e75; // Very Dark Teal
  --color-tl7: #0891b2; // Main Teal
  --color-tl6: #0ea5e9; // Medium Teal
  --color-tl5: #38bdf8; // Light Teal
  --color-tl4: #7dd3fc; // Lighter Teal
  --color-tl3: #bae6fd; // Very Light Teal
  --color-tl2: #e0f2fe; // Ultra Light Teal
  --color-tl1: #f0f9ff; // Lightest Teal

  // ===== SEMANTIC COLORS =====
  --color-semantic-info: var(--color-blue-main);
  --color-semantic-success: var(--color-green-main);
  --color-semantic-warning: #efae54;
  --color-semantic-error: var(--color-red-main);
  --color-semantic-neutral: var(--color-by6);

  // ===== STATUS COLORS =====
  --color-status-in-progress: var(--color-bu6);
  --color-status-incomplete: var(--color-by6);
  --color-status-complete: var(--color-gn6);
  --color-status-cancelled: var(--color-rd6);
  --color-status-pending: var(--color-oe6);
  --color-status-draft: var(--color-pu6);

  // ===== TRANSACTION COLORS =====
  --color-transaction-buy: var(--color-red-main);
  --color-transaction-sell: var(--color-blue-main);
  --color-transaction-cancel: var(--color-by6);
  --color-transaction-hold: var(--color-oe6);

  // ===== DEEP GRADIENTS =====
  --gradient-blue-deep: linear-gradient(135deg, var(--color-bu8) 0%, var(--color-bu6) 100%);
  --gradient-red-deep: linear-gradient(135deg, var(--color-rd8) 0%, var(--color-rd6) 100%);
  --gradient-green-deep: linear-gradient(135deg, var(--color-gn8) 0%, var(--color-gn6) 100%);
  --gradient-orange-deep: linear-gradient(135deg, var(--color-oe8) 0%, var(--color-oe6) 100%);
  --gradient-purple-deep: linear-gradient(135deg, var(--color-pu8) 0%, var(--color-pu6) 100%);
  --gradient-teal-deep: linear-gradient(135deg, var(--color-tl8) 0%, var(--color-tl6) 100%);

  // ===== LIGHT GRADIENTS =====
  --gradient-blue-light: linear-gradient(135deg, var(--color-bu2) 0%, var(--color-bu1) 100%);
  --gradient-red-light: linear-gradient(135deg, var(--color-rd2) 0%, var(--color-rd1) 100%);
  --gradient-green-light: linear-gradient(135deg, var(--color-gn2) 0%, var(--color-gn1) 100%);
  --gradient-orange-light: linear-gradient(135deg, var(--color-oe2) 0%, var(--color-oe1) 100%);
  --gradient-purple-light: linear-gradient(135deg, var(--color-pu2) 0%, var(--color-pu1) 100%);
  --gradient-teal-light: linear-gradient(135deg, var(--color-tl2) 0%, var(--color-tl1) 100%);
  --gradient-gold-light: linear-gradient(135deg, var(--color-gd2) 0%, var(--color-gd1) 100%);
  --gradient-gray-light: linear-gradient(135deg, var(--color-by2) 0%, var(--color-by1) 100%);

  // ===== MEDIUM GRADIENTS =====
  --gradient-blue-medium: linear-gradient(135deg, var(--color-bu4) 0%, var(--color-bu2) 100%);
  --gradient-red-medium: linear-gradient(135deg, var(--color-rd4) 0%, var(--color-rd2) 100%);
  --gradient-green-medium: linear-gradient(135deg, var(--color-gn4) 0%, var(--color-gn2) 100%);
  --gradient-orange-medium: linear-gradient(135deg, var(--color-oe4) 0%, var(--color-oe2) 100%);
  --gradient-purple-medium: linear-gradient(135deg, var(--color-pu4) 0%, var(--color-pu2) 100%);
  --gradient-teal-medium: linear-gradient(135deg, var(--color-tl4) 0%, var(--color-tl2) 100%);

  // ===== BRAND ICON GRADIENTS =====
  --gradient-galaxy-red: linear-gradient(45deg, #d94c4c 0%, #e67267 50%, #f28e85 100%);
  --gradient-galaxy-blue: linear-gradient(45deg, #5761d9 0%, #8594f2 50%, #a9aff1 100%);
  --gradient-wealth-gold: linear-gradient(45deg, #eba21e 0%, #f2ce85 50%, #f7d9a3 100%);
  --gradient-tech-blue: linear-gradient(45deg, #4470f5 0%, #85abf6 50%, #b3c7f7 100%);
  --gradient-vitality-orange: linear-gradient(45deg, #eca05a 0%, #fcb371 50%, #fdcb88 100%);
  --gradient-nature-green: linear-gradient(45deg, #248360 0%, #4db396 50%, #7ccbb3 100%);
  --gradient-royal-purple: linear-gradient(45deg, #7c3aed 0%, #a78bfa 50%, #c4b5fd 100%);
  --gradient-ocean-teal: linear-gradient(45deg, #0891b2 0%, #38bdf8 50%, #7dd3fc 100%);

  // ===== RAINBOW GRADIENTS =====
  --gradient-rainbow-warm: linear-gradient(
    90deg,
    #d94c4c 0%,
    #eba21e 25%,
    #f2ce85 50%,
    #248360 75%,
    #434dbf 100%
  );
  --gradient-rainbow-cool: linear-gradient(
    90deg,
    #434dbf 0%,
    #7c3aed 25%,
    #0891b2 50%,
    #248360 75%,
    #eba21e 100%
  );
  --gradient-sunset: linear-gradient(135deg, #d94c4c 0%, #eba21e 50%, #f2ce85 100%);
  --gradient-ocean: linear-gradient(135deg, #434dbf 0%, #0891b2 50%, #248360 100%);
  --gradient-forest: linear-gradient(135deg, #248360 0%, #7c3aed 50%, #434dbf 100%);
}

// ===== DARK MODE OVERRIDES =====
@media (prefers-color-scheme: dark) {
  :root {
    // Invert gray scale for dark mode
    --color-by8: #ffffff;
    --color-by7: #a3acbf;
    --color-by6: #656870;
    --color-by5: #4a4d5a;
    --color-by4: #2a2d3a;
    --color-by3: #1f2937;
    --color-by2: #111827;
    --color-by1: #090a1a;
    --color-bg: #0f1419;

    // Adjust other colors for dark mode
    --color-bu1: #1e293b;
    --color-rd1: #1f1b1b;
    --color-gn1: #1b2b26;
    --color-oe1: #2b251f;
    --color-pu1: #1e1b2e;
    --color-tl1: #1b252b;
    --color-gd1: #2b261f;
  }
}

// ===== SCSS COLOR MAPS FOR BACKWARD COMPATIBILITY =====

$brand-colors: (
  'blue-main': var(--color-blue-main),
  'orange-main': var(--color-orange-main),
  'red-main': var(--color-red-main),
  'green-main': var(--color-green-main),
);

$gray-colors: (
  'by8': var(--color-by8),
  'by7': var(--color-by7),
  'by6': var(--color-by6),
  'by5': var(--color-by5),
  'by4': var(--color-by4),
  'by3': var(--color-by3),
  'by2': var(--color-by2),
  'by1': var(--color-by1),
  'bg': var(--color-bg),
);

$blue-colors: (
  'bu9': var(--color-bu9),
  'bu8': var(--color-bu8),
  'bu7': var(--color-bu7),
  'bu6': var(--color-bu6),
  'bu5': var(--color-bu5),
  'bu4': var(--color-bu4),
  'bu3': var(--color-bu3),
  'bu2': var(--color-bu2),
  'bu1': var(--color-bu1),
);

$colors: map-merge(map-merge($brand-colors, $gray-colors), $blue-colors);

// ===== GRADIENT MAPS =====
$deep-gradients: (
  'blue': var(--gradient-blue-deep),
  'red': var(--gradient-red-deep),
  'green': var(--gradient-green-deep),
  'orange': var(--gradient-orange-deep),
  'purple': var(--gradient-purple-deep),
  'teal': var(--gradient-teal-deep),
);

$light-gradients: (
  'blue': var(--gradient-blue-light),
  'red': var(--gradient-red-light),
  'green': var(--gradient-green-light),
  'orange': var(--gradient-orange-light),
  'purple': var(--gradient-purple-light),
  'teal': var(--gradient-teal-light),
  'gold': var(--gradient-gold-light),
  'gray': var(--gradient-gray-light),
);

$brand-gradients: (
  'galaxy-red': var(--gradient-galaxy-red),
  'galaxy-blue': var(--gradient-galaxy-blue),
  'wealth-gold': var(--gradient-wealth-gold),
  'tech-blue': var(--gradient-tech-blue),
  'vitality-orange': var(--gradient-vitality-orange),
  'nature-green': var(--gradient-nature-green),
  'royal-purple': var(--gradient-royal-purple),
  'ocean-teal': var(--gradient-ocean-teal),
);
