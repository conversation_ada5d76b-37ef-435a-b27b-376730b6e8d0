// =================================================================
// Galaxy Design System v3.0 - Spacing Tokens
// =================================================================

:root {
  // Base spacing unit (4px)
  --space-unit: 0.25rem;
  
  // Spacing Scale
  --space-0: 0;
  --space-1: calc(var(--space-unit) * 1);   // 4px
  --space-2: calc(var(--space-unit) * 2);   // 8px
  --space-3: calc(var(--space-unit) * 3);   // 12px
  --space-4: calc(var(--space-unit) * 4);   // 16px
  --space-5: calc(var(--space-unit) * 5);   // 20px
  --space-6: calc(var(--space-unit) * 6);   // 24px
  --space-8: calc(var(--space-unit) * 8);   // 32px
  --space-10: calc(var(--space-unit) * 10); // 40px
  --space-12: calc(var(--space-unit) * 12); // 48px
  --space-16: calc(var(--space-unit) * 16); // 64px
  --space-20: calc(var(--space-unit) * 20); // 80px
  --space-24: calc(var(--space-unit) * 24); // 96px

  // Border Radius
  --radius-none: 0;
  --radius-sm: 0.125rem;   // 2px
  --radius-base: 0.25rem;  // 4px
  --radius-md: 0.375rem;   // 6px
  --radius-lg: 0.5rem;     // 8px
  --radius-xl: 0.75rem;    // 12px
  --radius-2xl: 1rem;      // 16px
  --radius-full: 9999px;

  // Shadows
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-base: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  // Transitions
  --transition-fast: 150ms ease-in-out;
  --transition-base: 200ms ease-in-out;
  --transition-slow: 300ms ease-in-out;
}

// Spacing utilities map
$spacing-scale: (
  0: var(--space-0),
  1: var(--space-1),
  2: var(--space-2),
  3: var(--space-3),
  4: var(--space-4),
  5: var(--space-5),
  6: var(--space-6),
  8: var(--space-8),
  10: var(--space-10),
  12: var(--space-12),
  16: var(--space-16),
  20: var(--space-20),
  24: var(--space-24)
);
