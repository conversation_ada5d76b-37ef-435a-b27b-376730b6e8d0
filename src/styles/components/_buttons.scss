// =================================================================
// Galaxy Design System v3.0 - Button Components
// =================================================================

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-2);
  
  font-weight: var(--font-weight-medium);
  text-align: center;
  vertical-align: middle;
  cursor: pointer;
  user-select: none;
  white-space: nowrap;
  
  background-color: transparent;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  
  transition: all var(--transition-base);
  
  // Sizes
  &--sm {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
    border-radius: var(--radius-md);
  }
  
  &--md {
    padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-base);
    line-height: var(--line-height-base);
  }
  
  &--lg {
    padding: var(--space-4) var(--space-6);
    font-size: var(--font-size-lg);
    line-height: var(--line-height-lg);
  }
  
  // Variants
  &--primary {
    color: var(--color-by1);
    background-color: var(--color-blue-main);
    border-color: var(--color-blue-main);
    
    &:hover:not(:disabled) {
      background-color: var(--color-bu6);
      border-color: var(--color-bu6);
    }
    
    &:active {
      background-color: var(--color-bu8);
      border-color: var(--color-bu8);
    }
  }
  
  &--secondary {
    color: var(--color-by8);
    background-color: var(--color-by1);
    border-color: var(--color-by4);
    
    &:hover:not(:disabled) {
      background-color: var(--color-by2);
      border-color: var(--color-by6);
    }
  }
  
  &--danger {
    color: var(--color-by1);
    background-color: var(--color-red-main);
    border-color: var(--color-red-main);
    
    &:hover:not(:disabled) {
      background-color: var(--color-rd8);
      border-color: var(--color-rd8);
    }
  }
  
  &--ghost {
    color: var(--color-by8);
    background-color: transparent;
    border-color: transparent;
    
    &:hover:not(:disabled) {
      background-color: var(--color-by2);
    }
  }
  
  &--link {
    color: var(--color-blue-main);
    background-color: transparent;
    border-color: transparent;
    text-decoration: underline;
    
    &:hover:not(:disabled) {
      color: var(--color-bu6);
    }
  }
  
  // States
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
  }
  
  &--loading {
    position: relative;
    color: transparent;
    
    &::after {
      content: '';
      position: absolute;
      width: 1rem;
      height: 1rem;
      border: 2px solid currentColor;
      border-radius: 50%;
      border-top-color: transparent;
      animation: spin 1s linear infinite;
    }
  }
  
  // Icon buttons
  &--icon-only {
    padding: var(--space-3);
    
    &.btn--sm {
      padding: var(--space-2);
    }
    
    &.btn--lg {
      padding: var(--space-4);
    }
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// Button group
.btn-group {
  display: inline-flex;
  
  .btn {
    border-radius: 0;
    
    &:first-child {
      border-top-left-radius: var(--radius-lg);
      border-bottom-left-radius: var(--radius-lg);
    }
    
    &:last-child {
      border-top-right-radius: var(--radius-lg);
      border-bottom-right-radius: var(--radius-lg);
    }
    
    &:not(:first-child) {
      border-left-width: 0;
    }
  }
}
