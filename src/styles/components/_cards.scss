// =================================================================
// Galaxy Design System v3.0 - Card Components
// =================================================================

.card {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: var(--color-by1);
  background-clip: border-box;
  border: 1px solid var(--color-by3);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  
  // Card variants
  &--elevated {
    box-shadow: var(--shadow-md);
    border: none;
  }
  
  &--outlined {
    box-shadow: none;
    border: 2px solid var(--color-by4);
  }
  
  &--interactive {
    cursor: pointer;
    transition: all var(--transition-base);
    
    &:hover {
      box-shadow: var(--shadow-lg);
      transform: translateY(-2px);
    }
  }
  
  // Card sections
  &__header {
    padding: var(--space-6);
    border-bottom: 1px solid var(--color-by3);
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  &__body {
    padding: var(--space-6);
    flex: 1 1 auto;
  }
  
  &__footer {
    padding: var(--space-6);
    border-top: 1px solid var(--color-by3);
    
    &:first-child {
      border-top: none;
    }
  }
  
  // Compact variant
  &--compact {
    .card__header,
    .card__body,
    .card__footer {
      padding: var(--space-4);
    }
  }
  
  // Media card
  &__media {
    width: 100%;
    height: auto;
    object-fit: cover;
  }
  
  // Card title and text
  &__title {
    @include text-style('lg', 'semibold');
    margin-bottom: var(--space-2);
    color: var(--color-by8);
  }
  
  &__subtitle {
    @include text-style('base', 'medium');
    margin-bottom: var(--space-3);
    color: var(--color-by7);
  }
  
  &__text {
    @include text-style('base', 'normal');
    color: var(--color-by7);
    
    &:not(:last-child) {
      margin-bottom: var(--space-4);
    }
  }
}

// Card grid
.card-grid {
  display: grid;
  gap: var(--space-6);
  
  &--cols-1 { grid-template-columns: 1fr; }
  &--cols-2 { grid-template-columns: repeat(2, 1fr); }
  &--cols-3 { grid-template-columns: repeat(3, 1fr); }
  &--cols-4 { grid-template-columns: repeat(4, 1fr); }
  
  @media (max-width: 768px) {
    &--cols-2,
    &--cols-3,
    &--cols-4 {
      grid-template-columns: 1fr;
    }
  }
  
  @media (min-width: 769px) and (max-width: 1024px) {
    &--cols-3,
    &--cols-4 {
      grid-template-columns: repeat(2, 1fr);
    }
  }
}
