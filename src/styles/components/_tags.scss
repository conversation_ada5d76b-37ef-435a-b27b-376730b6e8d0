// =================================================================
// Galaxy Design System v3.0 - Tag Components
// =================================================================

.tag {
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
  
  padding: var(--space-1) var(--space-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  line-height: var(--line-height-xs);
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  
  // Sizes
  &--sm {
    padding: var(--space-1) var(--space-2);
    font-size: 0.6875rem; // 11px
    border-radius: var(--radius-base);
  }
  
  &--lg {
    padding: var(--space-2) var(--space-4);
    font-size: var(--font-size-sm);
    line-height: var(--line-height-sm);
  }
  
  // Color variants
  &--blue {
    color: var(--color-blue-main);
    background-color: var(--color-bu2);
    border-color: var(--color-bu4);
  }
  
  &--orange {
    color: var(--color-oe8);
    background-color: var(--color-oe3);
    border-color: var(--color-oe4);
  }
  
  &--red {
    color: var(--color-red-main);
    background-color: var(--color-rd2);
    border-color: var(--color-rd5);
  }
  
  &--green {
    color: var(--color-green-main);
    background-color: #E6F7F1; // Light green background
    border-color: #B3E5D1; // Light green border
  }
  
  &--gray {
    color: var(--color-by7);
    background-color: var(--color-by2);
    border-color: var(--color-by4);
  }
  
  // Status variants
  &--success {
    color: var(--color-green-main);
    background-color: #E6F7F1;
    border-color: #B3E5D1;
  }
  
  &--warning {
    color: var(--color-semantic-warning);
    background-color: var(--color-oe3);
    border-color: var(--color-oe4);
  }
  
  &--error {
    color: var(--color-semantic-error);
    background-color: var(--color-rd2);
    border-color: var(--color-rd5);
  }
  
  &--info {
    color: var(--color-semantic-info);
    background-color: var(--color-bu2);
    border-color: var(--color-bu4);
  }
  
  // Interactive tags
  &--interactive {
    cursor: pointer;
    transition: all var(--transition-fast);
    
    &:hover {
      opacity: 0.8;
      transform: translateY(-1px);
    }
  }
  
  // Removable tags
  &--removable {
    padding-right: var(--space-1);
  }
  
  &__remove {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1rem;
    height: 1rem;
    margin-left: var(--space-1);
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: background-color var(--transition-fast);
    
    &:hover {
      background-color: rgba(0, 0, 0, 0.2);
    }
    
    &::before {
      content: '×';
      font-size: 0.75rem;
      line-height: 1;
    }
  }
}

// Tag group
.tag-group {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-2);
  align-items: center;
}
