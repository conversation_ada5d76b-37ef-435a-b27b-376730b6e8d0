<script setup lang="ts">
import { ref } from 'vue'
import TheWelcome from '../components/TheWelcome.vue'
import GalaxyCountTo from '../compat/galaxy-count-to.vue'

const manualCounterRef = ref<InstanceType<typeof GalaxyCountTo> | null>(null)

const startCount = () => {
  manualCounterRef.value?.start()
}

const pauseCount = () => {
  manualCounterRef.value?.pause()
}

const
  resumeCount = () => {
  manualCounterRef.value?.resume()
}

const resetCount = () => {
  manualCounterRef.value?.reset()
}

const restartCount = () => {
  manualCounterRef.value?.restart()
}

// 事件处理 - 兼容 vue-count-to 的事件名
const onMountedCallback = () => {
  console.log('CountTo component mounted! (vue-count-to compatible event)')
}

const onCallback = () => {
  console.log('CountTo animation finished! (vue-count-to compatible event)')
}

// vue3-count-to 的事件名
const onMounted = () => {
  console.log('CountTo component mounted! (vue3-count-to event)')
}

const onFinished = () => {
  console.log('CountTo animation finished! (vue3-count-to event)')
}
</script>

<template>
  <main>
    <div style="padding: 20px;">
      <h1>Galaxy Count To Component Demo</h1>
      <p style="color: #666; margin-bottom: 30px;">
        这是一个兼容 vue-count-to API 的 Vue 3 组件，基于 vue3-count-to 实现
      </p>

      <!-- 基础用法 - 使用 vue-count-to 风格的 camelCase 属性 -->
      <div style="margin: 20px 0;">
        <h2>基础用法 (vue-count-to 风格)</h2>
        <p>从 0 计数到 1000，使用 camelCase 属性：</p>
        <GalaxyCountTo :startVal="0" :endVal="1000" :duration="3000" />
      </div>

      <!-- 使用 kebab-case 属性 -->
      <div style="margin: 20px 0;">
        <h2>Vue 3 风格 (kebab-case 属性)</h2>
        <p>从 0 计数到 1000，使用 kebab-case 属性：</p>
        <GalaxyCountTo :start-val="0" :end-val="1000" :duration="3000" />
      </div>

      <!-- 带前缀后缀 -->
      <div style="margin: 20px 0;">
        <h2>带前缀和后缀</h2>
        <p>价格显示：</p>
        <GalaxyCountTo
          :startVal="0"
          :endVal="9999"
          :duration="4000"
          :decimals="2"
          prefix="¥"
          suffix=" 元"
          separator=","
        />
      </div>

      <!-- 倒计时 -->
      <div style="margin: 20px 0;">
        <h2>倒计时</h2>
        <p>从 100 倒数到 0：</p>
        <GalaxyCountTo
          :startVal="100"
          :endVal="0"
          :duration="5000"
          @mountedCallback="onMountedCallback"
          @callback="onCallback"
          @mounted="onMounted"
          @finished="onFinished"
        />
      </div>

      <!-- 手动控制 -->
      <div style="margin: 20px 0;">
        <h2>手动控制</h2>
        <p>手动控制动画（兼容 vue-count-to 的控制方法）：</p>
        <div style="font-size: 24px; font-weight: bold; color: #42b883; margin: 10px 0;">
          <GalaxyCountTo
            ref="manualCounterRef"
            :startVal="0"
            :endVal="5000"
            :duration="10000"
            :autoplay="false"
            separator=","
          />
        </div>
        <div style="margin-top: 10px;">
          <button @click="startCount" style="margin-right: 10px;">开始</button>
          <button @click="pauseCount" style="margin-right: 10px;">暂停</button>
          <button @click="resumeCount" style="margin-right: 10px;">恢复</button>
          <button @click="resetCount" style="margin-right: 10px;">重置</button>
          <button @click="restartCount">重新开始</button>
        </div>
      </div>

      <!-- 百分比显示 -->
      <div style="margin: 20px 0;">
        <h2>百分比显示</h2>
        <p>进度百分比：</p>
        <div style="font-size: 28px; font-weight: bold; color: #e74c3c;">
          <GalaxyCountTo
            :startVal="0"
            :endVal="100"
            :duration="3000"
            :decimals="1"
            suffix="%"
          />
        </div>
      </div>

      <!-- 自定义缓动函数 -->
      <div style="margin: 20px 0;">
        <h2>线性动画（无缓动）</h2>
        <p>线性增长到 888：</p>
        <GalaxyCountTo
          :startVal="0"
          :endVal="888"
          :duration="4000"
          :useEasing="false"
        />
      </div>

      <!-- 大数字格式化 -->
      <div style="margin: 20px 0;">
        <h2>大数字格式化</h2>
        <p>大数字带千位分隔符：</p>
        <div style="font-size: 32px; font-weight: bold; color: #8e44ad;">
          <GalaxyCountTo
            :startVal="0"
            :endVal="1234567"
            :duration="5000"
            separator=","
            prefix="$"
          />
        </div>
      </div>

      <div style="margin: 40px 0; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
        <h3>特性说明</h3>
        <ul style="text-align: left; color: #666;">
          <li>✅ 完全兼容 vue-count-to 的 camelCase 属性（如 startVal, endVal）</li>
          <li>✅ 支持 Vue 3 推荐的 kebab-case 属性（如 start-val, end-val）</li>
          <li>✅ 兼容 vue-count-to 的事件名（mountedCallback, callback）</li>
          <li>✅ 同时支持 vue3-count-to 的事件名（mounted, finished）</li>
          <li>✅ 保持所有原有的控制方法（start, pause, resume, reset, restart）</li>
          <li>✅ 基于成熟的 vue3-count-to 库，确保稳定性和性能</li>
        </ul>
      </div>
    </div>

    <TheWelcome />
  </main>
</template>

<style scoped>
button {
  padding: 8px 16px;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

button:hover {
  background-color: #369870;
}

button:active {
  transform: translateY(1px);
}

h1, h2, h3 {
  color: #2c3e50;
}

h1 {
  text-align: center;
  margin-bottom: 10px;
}

h2 {
  border-bottom: 2px solid #42b883;
  padding-bottom: 5px;
  margin-top: 30px;
}

p {
  margin: 10px 0;
  color: #7f8c8d;
}

ul {
  padding-left: 20px;
}

li {
  margin: 8px 0;
  line-height: 1.5;
}
</style>
