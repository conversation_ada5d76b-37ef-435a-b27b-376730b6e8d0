<script setup lang="ts">
import { ref } from 'vue'
import { ElButton, ElCard, ElIcon } from 'element-plus'
import {
  VideoPlay,
  VideoPause,
  RefreshRight,
  Switch,
  Position
} from '@element-plus/icons-vue'
import GalaxyCountTo from '../compat/galaxy-count-to.vue'
import TheWelcome from '../components/TheWelcome.vue'

const manualCounterRef = ref<InstanceType<typeof GalaxyCountTo> | null>(null)

const startCount = () => {
  manualCounterRef.value?.start()
}

const pauseCount = () => {
  manualCounterRef.value?.pause()
}

const resumeCount = () => {
  manualCounterRef.value?.resume()
}

const resetCount = () => {
  manualCounterRef.value?.reset()
}

const restartCount = () => {
  manualCounterRef.value?.restart()
}

// 事件处理 - 兼容 vue-count-to 的事件名
const onMountedCallback = () => {
  console.log('CountTo component mounted! (vue-count-to compatible event)')
}

const onCallback = () => {
  console.log('CountTo animation finished! (vue-count-to compatible event)')
}

// vue3-count-to 的事件名
const onMounted = () => {
  console.log('CountTo component mounted! (vue3-count-to event)')
}

const onFinished = () => {
  console.log('CountTo animation finished! (vue3-count-to event)')
}
</script>

<template>
  <main class="count-to-demo">
    <div class="container">
      <header class="demo-header">
        <h1>Galaxy Count To Component Demo</h1>
        <p>这是一个兼容 vue-count-to API 的 Vue 3 组件，基于 vue3-count-to 实现</p>
      </header>

      <el-card class="demo-section">
        <template #header>
          <h2>基础用法 (vue-count-to 风格)</h2>
        </template>
        <p>从 0 计数到 1000，使用 camelCase 属性：</p>
        <div class="demo-content">
          <GalaxyCountTo :startVal="0" :endVal="1000" :duration="3000" />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>Vue 3 风格 (kebab-case 属性)</h2>
        </template>
        <p>从 0 计数到 1000，使用 kebab-case 属性：</p>
        <div class="demo-content">
          <GalaxyCountTo :start-val="0" :end-val="1000" :duration="3000" />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>带前缀和后缀</h2>
        </template>
        <p>价格显示：</p>
        <div class="demo-content currency">
          <GalaxyCountTo
            :startVal="0"
            :endVal="9999"
            :duration="4000"
            :decimals="2"
            prefix="¥"
            suffix=" 元"
            separator=","
          />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>倒计时</h2>
        </template>
        <p>从 100 倒数到 0：</p>
        <div class="demo-content countdown">
          <GalaxyCountTo
            :startVal="100"
            :endVal="0"
            :duration="5000"
            @mountedCallback="onMountedCallback"
            @callback="onCallback"
            @mounted="onMounted"
            @finished="onFinished"
          />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>手动控制</h2>
        </template>
        <p>手动控制动画（兼容 vue-count-to 的控制方法）：</p>
        <div class="demo-content manual-control">
          <GalaxyCountTo
            ref="manualCounterRef"
            :startVal="0"
            :endVal="5000"
            :duration="10000"
            :autoplay="false"
            separator=","
          />
        </div>
        <div class="button-group">
          <el-button type="primary" @click="startCount">
            <el-icon><VideoPlay /></el-icon>开始
          </el-button>
          <el-button @click="pauseCount">
            <el-icon><VideoPause /></el-icon>暂停
          </el-button>
          <el-button @click="resumeCount">
            <el-icon><Position /></el-icon>恢复
          </el-button>
          <el-button @click="resetCount">
            <el-icon><Switch /></el-icon>重置
          </el-button>
          <el-button @click="restartCount">
            <el-icon><RefreshRight /></el-icon>重新开始
          </el-button>
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>百分比显示</h2>
        </template>
        <p>进度百分比：</p>
        <div class="demo-content percentage">
          <GalaxyCountTo
            :startVal="0"
            :endVal="100"
            :duration="3000"
            :decimals="1"
            suffix="%"
          />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>线性动画（无缓动）</h2>
        </template>
        <p>线性增长到 888：</p>
        <div class="demo-content">
          <GalaxyCountTo
            :startVal="0"
            :endVal="888"
            :duration="4000"
            :useEasing="false"
          />
        </div>
      </el-card>

      <el-card class="demo-section">
        <template #header>
          <h2>大数字格式化</h2>
        </template>
        <p>大数字带千位分隔符：</p>
        <div class="demo-content large-number">
          <GalaxyCountTo
            :startVal="0"
            :endVal="1234567"
            :duration="5000"
            separator=","
            prefix="$"
          />
        </div>
      </el-card>

      <el-card class="feature-list">
        <template #header>
          <h3>特性说明</h3>
        </template>
        <ul>
          <li>✅ 完全兼容 vue-count-to 的 camelCase 属性（如 startVal, endVal）</li>
          <li>✅ 支持 Vue 3 推荐的 kebab-case 属性（如 start-val, end-val）</li>
          <li>✅ 兼容 vue-count-to 的事件名（mountedCallback, callback）</li>
          <li>✅ 同时支持 vue3-count-to 的事件名（mounted, finished）</li>
          <li>✅ 保持所有原有的控制方法（start, pause, resume, reset, restart）</li>
          <li>✅ 基于成熟的 vue3-count-to 库，确保稳定性和性能</li>
        </ul>
      </el-card>
    </div>

    <TheWelcome />
  </main>
</template>

<style lang="scss" scoped>
.count-to-demo {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--space-4);
  }

  .demo-header {
    text-align: center;
    margin-bottom: var(--space-8);

    h1 {
      @include heading-2;
      color: var(--color-by8);
      margin-bottom: var(--space-4);
    }

    p {
      @include body-large;
      color: var(--color-by7);
    }
  }

  .demo-section {
    margin-bottom: var(--space-6);

    :deep(.el-card__header) {
      padding: var(--space-4) var(--space-6);
      border-bottom: 2px solid var(--color-bu2);

      h2 {
        @include heading-5;
        color: var(--color-by8);
        margin: 0;
      }
    }

    .demo-content {
      margin: var(--space-4) 0;
      font-family: var(--font-family-numeric);
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-semibold);
      color: var(--color-bu7);
      text-align: center;

      &.currency {
        color: var(--color-gn7);
      }

      &.countdown {
        color: var(--color-rd7);
      }

      &.percentage {
        color: var(--color-oe7);
      }

      &.large-number {
        color: var(--color-pu7);
      }
    }

    p {
      @include body-base;
      color: var(--color-by7);
      margin: var(--space-2) 0;
    }
  }

  .button-group {
    display: flex;
    gap: var(--space-2);
    justify-content: center;
    margin-top: var(--space-4);
    flex-wrap: wrap;

    .el-button {
      .el-icon {
        margin-right: var(--space-2);
      }
    }
  }

  .feature-list {
    background: var(--gradient-blue-light);

    ul {
      list-style: none;
      padding: 0;
      margin: var(--space-4) 0;

      li {
        @include body-base;
        color: var(--color-by7);
        margin: var(--space-2) 0;
        display: flex;
        align-items: center;
        gap: var(--space-2);

        &::before {
          content: '✓';
          color: var(--color-gn7);
          font-weight: var(--font-weight-bold);
        }
      }
    }
  }
}
</style>
